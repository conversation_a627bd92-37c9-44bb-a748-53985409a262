/**
 * Component Integration Validation
 *
 * This script validates that all batch management components
 * exist and have the correct file structure.
 */

const fs = require('fs');
const path = require('path');

// Test file existence and structure
try {
  console.log('🧪 Testing component file structure...');

  const componentDir = path.join(__dirname, '..');

  // List of required component files
  const requiredComponents = [
    'BatchManagementPageClient.tsx',
    'BatchValidationStats.tsx',
    'ComplianceReportCard.tsx',
    'BatchHistoryTable.tsx',
    'ExpiryTrackingTable.tsx',
    'ComplianceTable.tsx',
    'AuditLogTable.tsx',
    'batch-management-stats-cards.tsx'
  ];

  // Check if all component files exist
  for (const component of requiredComponents) {
    const filePath = path.join(componentDir, component);
    if (!fs.existsSync(filePath)) {
      throw new Error(`Component file ${component} does not exist`);
    }
    console.log(`✅ ${component} exists`);
  }

  // Check supporting files
  const srcDir = path.join(__dirname, '../../../');
  const supportingFiles = [
    'lib/api/batch-management.ts',
    'hooks/useBatchManagement.ts',
    'types/batch-management.ts',
    'lib/constants/batch-management.ts'
  ];

  for (const file of supportingFiles) {
    const filePath = path.join(srcDir, file);
    if (!fs.existsSync(filePath)) {
      throw new Error(`Supporting file ${file} does not exist`);
    }
    console.log(`✅ ${path.basename(file)} exists`);
  }

  console.log('\n🎉 All required files exist!');

  // Check file content structure (basic validation)
  console.log('\n🔍 Validating file content structure...');

  // Check if API file has export
  const apiFilePath = path.join(srcDir, 'lib/api/batch-management.ts');
  const apiContent = fs.readFileSync(apiFilePath, 'utf8');
  if (!apiContent.includes('export const batchManagementApi')) {
    throw new Error('API file does not export batchManagementApi');
  }
  console.log('✅ API file has correct export structure');

  // Check if hooks file has exports
  const hooksFilePath = path.join(srcDir, 'hooks/useBatchManagement.ts');
  const hooksContent = fs.readFileSync(hooksFilePath, 'utf8');
  if (!hooksContent.includes('export function useBatchManagementStats')) {
    throw new Error('Hooks file does not have expected exports');
  }
  console.log('✅ Hooks file has correct export structure');

  // Check if types file has exports
  const typesFilePath = path.join(srcDir, 'types/batch-management.ts');
  const typesContent = fs.readFileSync(typesFilePath, 'utf8');
  if (!typesContent.includes('export interface BatchManagementStats')) {
    throw new Error('Types file does not have expected exports');
  }
  console.log('✅ Types file has correct export structure');

  // Check if constants file has exports
  const constantsFilePath = path.join(srcDir, 'lib/constants/batch-management.ts');
  const constantsContent = fs.readFileSync(constantsFilePath, 'utf8');
  if (!constantsContent.includes('export const BATCH_STATUS_OPTIONS')) {
    throw new Error('Constants file does not have expected exports');
  }
  console.log('✅ Constants file has correct export structure');

  console.log('\n🎉 Component integration validation completed successfully!');
  console.log('\n📋 Summary:');
  console.log('- ✅ All component files exist');
  console.log('- ✅ All supporting files exist');
  console.log('- ✅ API client has correct export structure');
  console.log('- ✅ Hooks have correct export structure');
  console.log('- ✅ Types have correct export structure');
  console.log('- ✅ Constants have correct export structure');
  console.log('\n🚀 Batch management integration is ready for production!');

} catch (error) {
  console.error('\n❌ Component validation failed:', error instanceof Error ? error.message : String(error));
  console.error('\n🔧 Please fix the issues above before proceeding.');
  process.exit(1);
}
